#!/usr/bin/env python3
"""
Test script to verify ECG image processing fixes
"""

import numpy as np
from skimage.io import imread
from skimage import color
from skimage.transform import resize
from skimage.metrics import structural_similarity
import os

def test_structural_similarity_fix():
    """Test the structural similarity fix"""
    print("🧪 Testing structural similarity fix...")
    
    # Check if sample image exists
    if not os.path.exists("PMI(66).jpg"):
        print("❌ Sample image PMI(66).jpg not found")
        return False
    
    try:
        # Load and process image
        image = imread("PMI(66).jpg")
        image_gray = color.rgb2gray(image)
        image_gray = resize(image_gray, (1572, 2213))
        
        # Test structural similarity with data_range parameter
        similarity_score = structural_similarity(image_gray, image_gray, data_range=1.0)
        
        print(f"✅ Structural similarity test passed! Score: {similarity_score:.4f}")
        return True
        
    except Exception as e:
        print(f"❌ Structural similarity test failed: {e}")
        return False

def test_image_processing():
    """Test basic image processing pipeline"""
    print("🧪 Testing image processing pipeline...")
    
    if not os.path.exists("PMI(66).jpg"):
        print("❌ Sample image PMI(66).jpg not found")
        return False
    
    try:
        # Load image
        image = imread("PMI(66).jpg")
        print(f"✅ Image loaded: {image.shape}, dtype: {image.dtype}")
        
        # Convert to grayscale
        image_gray = color.rgb2gray(image)
        print(f"✅ Grayscale conversion: {image_gray.shape}, dtype: {image_gray.dtype}")
        
        # Resize
        image_resized = resize(image_gray, (1572, 2213))
        print(f"✅ Image resized: {image_resized.shape}, dtype: {image_resized.dtype}")
        
        # Check data range
        print(f"✅ Data range: min={image_resized.min():.4f}, max={image_resized.max():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Image processing test failed: {e}")
        return False

def test_lead_extraction():
    """Test ECG lead extraction"""
    print("🧪 Testing ECG lead extraction...")
    
    if not os.path.exists("PMI(66).jpg"):
        print("❌ Sample image PMI(66).jpg not found")
        return False
    
    try:
        # Load and process image
        image = imread("PMI(66).jpg")
        image_gray = color.rgb2gray(image)
        image_gray = resize(image_gray, (1572, 2213))
        
        # Test lead extraction positions
        lead_positions = [
            (300, 600, 150, 643),    # Lead 1
            (300, 600, 646, 1135),   # Lead 2
            (300, 600, 1140, 1625),  # Lead 3
        ]
        
        leads = []
        for i, (y1, y2, x1, x2) in enumerate(lead_positions):
            lead = image_gray[y1:y2, x1:x2]
            leads.append(lead)
            print(f"✅ Lead {i+1}: {lead.shape}, dtype: {lead.dtype}")
        
        print(f"✅ Lead extraction test passed! Extracted {len(leads)} leads")
        return True
        
    except Exception as e:
        print(f"❌ Lead extraction test failed: {e}")
        return False

def main():
    print("🫀 ECG Processing Test Suite")
    print("=" * 40)
    
    tests = [
        test_structural_similarity_fix,
        test_image_processing,
        test_lead_extraction
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The ECG processing fixes are working correctly.")
        print("✅ You can now upload ECG images without the data_range error.")
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")
    
    print("\n🚀 To run the app:")
    print("python -m streamlit run enhanced_ecg_app.py --server.port 8505")

if __name__ == "__main__":
    main()
