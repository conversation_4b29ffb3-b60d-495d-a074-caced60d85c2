from skimage.io import imread
from skimage import color
import matplotlib.pyplot as plt
import streamlit as st
from skimage.filters import threshold_otsu, gaussian
from skimage.transform import resize
from numpy import asarray
from skimage.metrics import structural_similarity
from skimage import measure
from sklearn.decomposition import PCA
from sklearn.neighbors import KNeighborsClassifier
import joblib
from sklearn.preprocessing import MinMaxScaler
import pandas as pd
import numpy as np
import os
from natsort import natsorted
import tempfile
import shutil
from pathlib import Path
import json
import pickle

# Try to import advanced features, fall back to basic functionality if not available
try:
    from langchain_community.embeddings import HuggingFaceEmbeddings
    from langchain_community.vectorstores import FAISS
    from langchain.text_splitter import RecursiveCharacterTextSplitter
    from langchain.schema import Document
    from langchain_community.llms import HuggingFacePipeline
    from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
    import torch
    ADVANCED_FEATURES = True
except ImportError as e:
    print(f"Advanced AI features not available: {e}. Running in basic mode.")
    ADVANCED_FEATURES = False

try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

# Set page config
st.set_page_config(
    page_title="ECG Analysis with AI Cardiologist",
    page_icon="🫀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'ecg_analysis_result' not in st.session_state:
    st.session_state.ecg_analysis_result = None
if 'vectorstore' not in st.session_state:
    st.session_state.vectorstore = None
if 'llm' not in st.session_state:
    st.session_state.llm = None

# ECG Knowledge Base
ECG_KNOWLEDGE_BASE = """
ECG (Electrocardiogram) Interpretation Guide:

NORMAL ECG:
- Regular rhythm with normal P waves, QRS complexes, and T waves
- Heart rate: 60-100 beats per minute
- PR interval: 120-200 ms
- QRS duration: <120 ms
- Normal axis deviation

MYOCARDIAL INFARCTION (MI):
- ST-segment elevation or depression
- Pathological Q waves
- T-wave inversions
- Elevated cardiac enzymes
- Chest pain symptoms
- Can be STEMI (ST-elevation MI) or NSTEMI (Non-ST-elevation MI)

ABNORMAL HEARTBEAT (Arrhythmias):
- Irregular rhythm patterns
- Atrial fibrillation: Irregular R-R intervals, absent P waves
- Ventricular tachycardia: Wide QRS complexes, rapid rate
- Bradycardia: Heart rate <60 bpm
- Tachycardia: Heart rate >100 bpm

HISTORY OF MYOCARDIAL INFARCTION:
- Old Q waves indicating previous heart attack
- Scarred tissue from previous MI
- May show regional wall motion abnormalities
- Requires ongoing cardiac monitoring

P WAVE: Represents atrial depolarization
QRS COMPLEX: Represents ventricular depolarization
T WAVE: Represents ventricular repolarization

CLINICAL SIGNIFICANCE:
- ECG is a non-invasive diagnostic tool
- Helps detect heart rhythm abnormalities
- Essential for emergency cardiac care
- Should always be interpreted by qualified medical professionals

IMPORTANT DISCLAIMER:
This AI system is for educational purposes only and should not replace professional medical diagnosis or treatment.
Always consult with a qualified cardiologist or healthcare provider for proper medical evaluation.
"""

FAQ_DATA = [
    {
        "question": "What is an ECG?",
        "answer": "An ECG (Electrocardiogram) is a test that records the electrical activity of your heart. It shows how your heart beats and can detect various heart conditions."
    },
    {
        "question": "What does a normal ECG look like?",
        "answer": "A normal ECG shows regular P waves, QRS complexes, and T waves with normal intervals and no abnormal patterns."
    },
    {
        "question": "What is myocardial infarction?",
        "answer": "Myocardial infarction (heart attack) occurs when blood flow to part of the heart muscle is blocked, causing tissue damage. ECG shows characteristic changes like ST elevation or Q waves."
    },
    {
        "question": "What are arrhythmias?",
        "answer": "Arrhythmias are abnormal heart rhythms that can be too fast, too slow, or irregular. They appear as irregular patterns on the ECG."
    },
    {
        "question": "Should I be worried about my ECG results?",
        "answer": "Always consult with a qualified cardiologist for proper interpretation. This AI tool is for educational purposes only and cannot replace professional medical advice."
    }
]

@st.cache_resource
def load_ecg_model():
    """Load the pre-trained ECG classification model"""
    try:
        model_path = "model_test.pkl"
        if os.path.exists(model_path):
            return joblib.load(model_path)
        else:
            st.error(f"Model file {model_path} not found!")
            return None
    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        return None

@st.cache_resource
def setup_rag_system():
    """Initialize the RAG system with ECG knowledge base"""
    if not ADVANCED_FEATURES:
        return None, None

    try:
        # Initialize embeddings (using a lightweight model)
        embeddings = HuggingFaceEmbeddings(
            model_name="sentence-transformers/all-MiniLM-L6-v2",
            model_kwargs={'device': 'cpu'}
        )

        # Create documents from knowledge base
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=50
        )

        # Split knowledge base into chunks
        kb_chunks = text_splitter.split_text(ECG_KNOWLEDGE_BASE)

        # Add FAQ data
        faq_texts = [f"Q: {faq['question']}\nA: {faq['answer']}" for faq in FAQ_DATA]

        # Combine all texts
        all_texts = kb_chunks + faq_texts

        # Create documents
        documents = [Document(page_content=text) for text in all_texts]

        # Create vector store
        vectorstore = FAISS.from_documents(documents, embeddings)

        return vectorstore, embeddings
    except Exception as e:
        st.error(f"Error setting up RAG system: {str(e)}")
        return None, None

@st.cache_resource
def setup_llm():
    """Setup local LLM for text generation"""
    if not ADVANCED_FEATURES:
        return None

    try:
        # Use a lightweight model that doesn't require API keys
        model_name = "microsoft/DialoGPT-medium"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(model_name)

        # Create pipeline
        pipe = pipeline(
            "text-generation",
            model=model,
            tokenizer=tokenizer,
            max_length=512,
            temperature=0.7,
            do_sample=True,
            device=-1  # Use CPU
        )

        llm = HuggingFacePipeline(pipeline=pipe)
        return llm
    except Exception as e:
        st.error(f"Error setting up LLM: {str(e)}")
        return None

def process_ecg_image(image):
    """Process ECG image and extract features"""
    try:
        # Convert to grayscale
        image_gray = color.rgb2gray(image)
        image_gray = resize(image_gray, (1572, 2213))
        
        # Divide into leads (12 leads + 1 long lead)
        leads = []
        
        # Extract 12 standard leads
        lead_positions = [
            (300, 600, 150, 643),    # Lead 1
            (300, 600, 646, 1135),   # Lead 2
            (300, 600, 1140, 1625),  # Lead 3
            (300, 600, 1630, 2125),  # Lead 4
            (600, 900, 150, 643),    # Lead 5
            (600, 900, 646, 1135),   # Lead 6
            (600, 900, 1140, 1625),  # Lead 7
            (600, 900, 1630, 2125),  # Lead 8
            (900, 1200, 150, 643),   # Lead 9
            (900, 1200, 646, 1135),  # Lead 10
            (900, 1200, 1140, 1625), # Lead 11
            (900, 1200, 1630, 2125), # Lead 12
        ]
        
        for i, (y1, y2, x1, x2) in enumerate(lead_positions):
            lead = image_gray[y1:y2, x1:x2]
            leads.append(lead)

        # Long lead (Lead 13)
        lead_13 = image_gray[1250:1480, 150:2125]
        leads.append(lead_13)
        
        return leads, image_gray
    except Exception as e:
        st.error(f"Error processing ECG image: {str(e)}")
        return None, None

def extract_signal_features(leads):
    """Extract 1D signal features from ECG leads"""
    try:
        all_features = []
        
        for i, lead in enumerate(leads[:-1]):  # Process first 12 leads
            # Convert to grayscale
            grayscale = color.rgb2gray(lead)
            
            # Apply Gaussian smoothing
            blurred_image = gaussian(grayscale, sigma=0.9)
            
            # Threshold using Otsu's method
            global_thresh = threshold_otsu(blurred_image)
            binary_global = blurred_image < global_thresh
            
            # Resize
            binary_global = resize(binary_global, (300, 450))
            
            # Find contours
            contours = measure.find_contours(binary_global, 0.8)
            
            if contours:
                # Get the largest contour
                contours_shape = sorted([x.shape for x in contours], reverse=True)
                if contours_shape:
                    for contour in contours:
                        if contour.shape == contours_shape[0]:
                            # Resize contour to standard size
                            signal = resize(contour, (255, 2))
                            
                            # Normalize the signal
                            scaler = MinMaxScaler()
                            normalized_signal = scaler.fit_transform(signal[:, 0].reshape(-1, 1))
                            all_features.extend(normalized_signal.flatten())
                            break
        
        return np.array(all_features).reshape(1, -1) if all_features else None
    except Exception as e:
        st.error(f"Error extracting signal features: {str(e)}")
        return None

def get_prediction_explanation(prediction, confidence=None):
    """Generate explanation for the ECG prediction"""
    explanations = {
        0: {
            "condition": "Myocardial Infarction (Heart Attack)",
            "explanation": "The ECG shows patterns consistent with acute myocardial infarction. This indicates blocked blood flow to heart muscle, requiring immediate medical attention.",
            "symptoms": "Chest pain, shortness of breath, nausea, sweating",
            "action": "URGENT: Seek immediate emergency medical care"
        },
        1: {
            "condition": "Abnormal Heartbeat (Arrhythmia)",
            "explanation": "The ECG shows irregular heart rhythm patterns. This could indicate various types of arrhythmias that may require monitoring or treatment.",
            "symptoms": "Palpitations, dizziness, fatigue, irregular pulse",
            "action": "Consult with a cardiologist for further evaluation"
        },
        2: {
            "condition": "Normal ECG",
            "explanation": "The ECG shows normal heart rhythm and electrical activity. All waves and intervals appear within normal ranges.",
            "symptoms": "No cardiac abnormalities detected",
            "action": "Continue regular health monitoring"
        },
        3: {
            "condition": "History of Myocardial Infarction",
            "explanation": "The ECG shows evidence of previous heart attack with characteristic Q waves or other changes indicating old myocardial damage.",
            "symptoms": "May be asymptomatic or have reduced exercise tolerance",
            "action": "Regular cardiac follow-up and medication compliance"
        }
    }
    
    return explanations.get(prediction, {
        "condition": "Unknown",
        "explanation": "Unable to classify this ECG pattern",
        "symptoms": "Consult healthcare provider",
        "action": "Seek professional medical evaluation"
    })

def answer_ecg_question(question, vectorstore, context=""):
    """Answer ECG-related questions using RAG or rule-based responses"""
    try:
        # If RAG is available, use it
        if vectorstore is not None and ADVANCED_FEATURES:
            # Retrieve relevant documents
            docs = vectorstore.similarity_search(question, k=3)
            retrieved_context = "\n".join([doc.page_content for doc in docs])
        else:
            retrieved_context = ECG_KNOWLEDGE_BASE

        # Create a comprehensive answer based on question keywords
        question_lower = question.lower()

        if "normal" in question_lower:
            answer = "A normal ECG shows regular P waves, QRS complexes, and T waves with normal intervals (PR: 120-200ms, QRS: <120ms). Heart rate should be 60-100 bpm with normal axis."
        elif "myocardial infarction" in question_lower or "heart attack" in question_lower or "mi" in question_lower:
            answer = "Myocardial infarction appears on ECG as ST-segment changes, pathological Q waves, and T-wave inversions. It indicates blocked blood flow to heart muscle requiring immediate medical attention."
        elif "arrhythmia" in question_lower or "abnormal" in question_lower or "irregular" in question_lower:
            answer = "Arrhythmias show as irregular rhythm patterns on ECG. Common types include atrial fibrillation (irregular R-R intervals), ventricular tachycardia (wide QRS), and bradycardia (<60 bpm)."
        elif "p wave" in question_lower:
            answer = "P waves represent atrial depolarization. Normal P waves are upright in leads I, II, and aVF, and should precede each QRS complex."
        elif "qrs" in question_lower:
            answer = "QRS complex represents ventricular depolarization. Normal duration is <120ms. Wide QRS may indicate bundle branch blocks or ventricular origin."
        elif "t wave" in question_lower:
            answer = "T waves represent ventricular repolarization. They should be upright in most leads. Inverted T waves may indicate ischemia or previous MI."
        elif "what" in question_lower and "ecg" in question_lower:
            answer = "An ECG (Electrocardiogram) is a test that records the electrical activity of your heart. It shows how your heart beats and can detect various heart conditions."
        elif "how" in question_lower and ("read" in question_lower or "interpret" in question_lower):
            answer = "ECG interpretation involves analyzing P waves (atrial activity), QRS complexes (ventricular activity), T waves (repolarization), and measuring intervals and rhythms."
        elif context and ("result" in question_lower or "analysis" in question_lower):
            answer = f"Based on your current ECG analysis: {context}. The AI detected specific patterns that led to this classification."
        else:
            # Generic response
            answer = "I can help explain ECG patterns, heart conditions, and interpretation. Please ask specific questions about ECG waves, heart rhythms, or cardiac conditions."

        # Add medical disclaimer
        answer += "\n\n⚠️ MEDICAL DISCLAIMER: This information is for educational purposes only. Always consult with a qualified healthcare provider for proper medical diagnosis and treatment."

        return answer
    except Exception as e:
        return f"Error answering question: {str(e)}"

# Main application
def main():
    st.title("🫀 ECG Analysis with AI Cardiologist Chatbot")
    st.markdown("---")
    
    # Setup RAG system
    if st.session_state.vectorstore is None:
        with st.spinner("Initializing AI Cardiologist..."):
            vectorstore, embeddings = setup_rag_system()
            st.session_state.vectorstore = vectorstore
    
    # Create layout
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📊 ECG Image Analysis")
        uploaded_file = st.file_uploader("Choose an ECG image file", type=['jpg', 'jpeg', 'png'])
        
        if uploaded_file is not None:
            # Process the uploaded image
            process_uploaded_ecg(uploaded_file)
    
    with col2:
        st.header("🤖 AI Cardiologist Chat")
        display_chat_interface()
    
    # FAQ Section
    st.markdown("---")
    st.header("❓ Frequently Asked Questions")
    display_faq_section()

def process_uploaded_ecg(uploaded_file):
    """Process uploaded ECG image and perform analysis"""
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            tmp_file.write(uploaded_file.read())
            temp_path = tmp_file.name
        
        # Load and process image
        image = imread(temp_path)
        image_gray = color.rgb2gray(image)
        image_gray = resize(image_gray, (1572, 2213))
        
        # Display uploaded image
        st.image(image, caption="Uploaded ECG Image", use_container_width=True)
        
        # Check similarity with reference (if available)
        similarity_score = 0.8  # Default to proceed
        reference_path = "PMI(66).jpg"
        if os.path.exists(reference_path):
            ref_image = imread(reference_path)
            ref_image_gray = color.rgb2gray(ref_image)
            ref_image_gray = resize(ref_image_gray, (1572, 2213))
            similarity_score = structural_similarity(image_gray, ref_image_gray, data_range=1.0)
        
        if similarity_score > 0.70:
            # Process ECG
            leads, processed_gray = process_ecg_image(image)
            
            if leads:
                # Display processing steps
                with st.expander("🔍 Image Processing Steps"):
                    st.image(processed_gray, caption="Grayscale Processed Image", use_container_width=True)
                
                # Extract features and make prediction
                features = extract_signal_features(leads)
                
                if features is not None:
                    # Load model and make prediction
                    model = load_ecg_model()
                    if model:
                        prediction = model.predict(features)[0]
                        
                        # Get prediction probabilities if available
                        try:
                            probabilities = model.predict_proba(features)[0]
                            confidence = max(probabilities)
                        except:
                            confidence = None
                        
                        # Store result in session state
                        st.session_state.ecg_analysis_result = {
                            'prediction': prediction,
                            'confidence': confidence,
                            'features': features
                        }
                        
                        # Display results
                        display_prediction_results(prediction, confidence)
                        
                        # Clean up temporary file
                        os.unlink(temp_path)
                    else:
                        st.error("Could not load the ECG classification model.")
                else:
                    st.error("Could not extract features from the ECG image.")
            else:
                st.error("Could not process the ECG leads from the image.")
        else:
            st.warning("The uploaded image doesn't appear to be in the expected ECG format. Please upload a standard 12-lead ECG image.")
            
    except Exception as e:
        st.error(f"Error processing ECG image: {str(e)}")

def display_prediction_results(prediction, confidence=None):
    """Display ECG prediction results with explanations"""
    explanation = get_prediction_explanation(prediction, confidence)

    # Create result display
    st.subheader("🏥 ECG Analysis Results")

    # Create columns for better layout
    result_col1, result_col2 = st.columns([2, 1])

    with result_col1:
        # Color-code based on severity
        if prediction == 0:  # MI
            st.error(f"**Condition:** {explanation['condition']}")
        elif prediction == 1:  # Abnormal
            st.warning(f"**Condition:** {explanation['condition']}")
        elif prediction == 2:  # Normal
            st.success(f"**Condition:** {explanation['condition']}")
        else:  # History of MI
            st.info(f"**Condition:** {explanation['condition']}")

        # Detailed explanation
        st.write("**Medical Explanation:**")
        st.write(explanation['explanation'])

        st.write("**Common Symptoms:**")
        st.write(explanation['symptoms'])

        st.write("**Recommended Action:**")
        st.write(explanation['action'])

    with result_col2:
        # Display confidence if available
        if confidence:
            st.metric("Prediction Confidence", f"{confidence:.2%}")

        # Add explainable AI visualization
        if SHAP_AVAILABLE and st.session_state.ecg_analysis_result:
            with st.expander("🔍 AI Explanation (SHAP)"):
                st.write("Feature importance analysis would be displayed here with SHAP values.")
                st.info("SHAP analysis requires model retraining with explainability features.")

        # Add prediction probabilities visualization
        if confidence:
            prob_data = {
                'MI': 0.1 if prediction != 0 else confidence,
                'Abnormal': 0.1 if prediction != 1 else confidence,
                'Normal': 0.1 if prediction != 2 else confidence,
                'History MI': 0.1 if prediction != 3 else confidence
            }

            # Normalize probabilities
            total = sum(prob_data.values())
            prob_data = {k: v/total for k, v in prob_data.items()}

            st.write("**Prediction Probabilities:**")
            for condition, prob in prob_data.items():
                st.progress(prob, text=f"{condition}: {prob:.1%}")

    # Medical disclaimer
    st.warning("⚠️ **IMPORTANT:** This AI analysis is for educational purposes only. Always consult with a qualified cardiologist or healthcare provider for proper medical diagnosis and treatment.")

def display_chat_interface():
    """Display the chatbot interface"""
    st.subheader("💬 Ask the AI Cardiologist")
    
    # Chat input
    user_question = st.text_input("Ask a question about ECG or cardiology:", key="chat_input")
    
    if st.button("Ask Question") and user_question:
        # Add user question to chat history
        st.session_state.chat_history.append({"role": "user", "content": user_question})
        
        # Get answer using RAG
        context = ""
        if st.session_state.ecg_analysis_result:
            context = f"Current ECG analysis shows: {get_prediction_explanation(st.session_state.ecg_analysis_result['prediction'])['condition']}"
        
        answer = answer_ecg_question(user_question, st.session_state.vectorstore, context)
        
        # Add AI response to chat history
        st.session_state.chat_history.append({"role": "assistant", "content": answer})
    
    # Display chat history
    if st.session_state.chat_history:
        st.subheader("Chat History")
        for i, message in enumerate(reversed(st.session_state.chat_history[-10:])):  # Show last 10 messages
            if message["role"] == "user":
                st.write(f"👤 **You:** {message['content']}")
            else:
                st.write(f"🤖 **AI Cardiologist:** {message['content']}")
            st.markdown("---")

def display_faq_section():
    """Display FAQ section with expandable answers"""
    for i, faq in enumerate(FAQ_DATA):
        with st.expander(f"❓ {faq['question']}"):
            st.write(faq['answer'])

if __name__ == "__main__":
    main()
