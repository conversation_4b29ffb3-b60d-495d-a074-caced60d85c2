
# This file was generated by 'versioneer.py' (0.14) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

version_version = '0.43.0'
version_full = '6c786059354260a0ae93f9d0144d4016ab3d63b4'
def get_versions(default={}, verbose=False):
    return {'version': version_version, 'full': version_full}

