#!/usr/bin/env python3
"""
Check dependencies and run the Enhanced ECG App
"""

import sys
import subprocess
import importlib

def check_package(package_name):
    """Check if a package is installed"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def main():
    print("🫀 Enhanced ECG Analysis App - Startup Check")
    print("=" * 50)
    
    # Check core packages
    core_packages = [
        ('streamlit', 'streamlit'),
        ('sklearn', 'scikit-learn'),
        ('skimage', 'scikit-image'),
        ('matplotlib', 'matplotlib'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('joblib', 'joblib')
    ]
    
    print("Checking core packages...")
    missing_core = []
    for pkg_import, pkg_name in core_packages:
        if check_package(pkg_import):
            print(f"✅ {pkg_name}")
        else:
            print(f"❌ {pkg_name} - MISSING")
            missing_core.append(pkg_name)
    
    # Check advanced packages
    advanced_packages = [
        ('langchain_community', 'langchain-community'),
        ('sentence_transformers', 'sentence-transformers'),
        ('faiss', 'faiss-cpu'),
        ('transformers', 'transformers'),
        ('torch', 'torch')
    ]
    
    print("\nChecking advanced AI packages...")
    missing_advanced = []
    for pkg_import, pkg_name in advanced_packages:
        if check_package(pkg_import):
            print(f"✅ {pkg_name}")
        else:
            print(f"⚠️ {pkg_name} - Missing (advanced features disabled)")
            missing_advanced.append(pkg_name)
    
    print("\n" + "=" * 50)
    
    if missing_core:
        print("❌ CRITICAL: Missing core packages. Please install:")
        print(f"pip install {' '.join(missing_core)}")
        return False
    
    if missing_advanced:
        print("⚠️ Some advanced AI features will be disabled.")
        print("To enable full features, install:")
        print(f"pip install {' '.join(missing_advanced)}")
    
    print("\n🚀 Starting Enhanced ECG App...")
    print("📱 The app will open in your browser automatically")
    print("🌐 Manual URL: http://localhost:8504")
    print("\n⏹️ Press Ctrl+C to stop the app")
    print("=" * 50)
    
    try:
        # Run streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "enhanced_ecg_app.py", 
            "--server.port", "8504",
            "--server.headless", "false"
        ])
    except KeyboardInterrupt:
        print("\n\n⏹️ App stopped by user")
    except Exception as e:
        print(f"\n❌ Error running app: {e}")
        print("\nTry running manually:")
        print("python -m streamlit run enhanced_ecg_app.py --server.port 8504")

if __name__ == "__main__":
    main()
