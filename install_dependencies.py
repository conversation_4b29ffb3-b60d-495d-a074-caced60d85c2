#!/usr/bin/env python3
"""
Installation script for ECG Analysis App dependencies
This script installs all required packages for the enhanced ECG application
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    print("🫀 ECG Analysis App - Dependency Installation")
    print("=" * 50)
    
    # Core packages (essential)
    core_packages = [
        "streamlit==1.35.0",
        "joblib==1.4.2",
        "matplotlib==3.9.0",
        "pandas==2.2.2",
        "scikit-learn==1.5.0",
        "scikit-image==0.23.2",
        "natsort==8.4.0",
        "numpy==1.24.3",
        "Pillow==10.3.0",
        "plotly==5.20.0"
    ]
    
    # Advanced packages (for RAG and AI features)
    advanced_packages = [
        "langchain==0.2.1",
        "langchain-community==0.2.1",
        "faiss-cpu==1.7.4",
        "transformers==4.41.2",
        "huggingface-hub==0.23.3",
        "sentence-transformers==2.7.0",
        "torch==2.3.1",
        "shap==0.45.0"
    ]
    
    print("Installing core packages...")
    core_success = 0
    for package in core_packages:
        if install_package(package):
            core_success += 1
    
    print(f"\nCore packages: {core_success}/{len(core_packages)} installed successfully")
    
    if core_success == len(core_packages):
        print("\n🎉 Core installation complete! You can run the basic version of the app.")
        
        # Ask user if they want to install advanced features
        try:
            install_advanced = input("\nDo you want to install advanced AI features (RAG, SHAP)? This may take longer. (y/n): ").lower().strip()
            
            if install_advanced in ['y', 'yes']:
                print("\nInstalling advanced packages...")
                advanced_success = 0
                for package in advanced_packages:
                    if install_package(package):
                        advanced_success += 1
                
                print(f"\nAdvanced packages: {advanced_success}/{len(advanced_packages)} installed successfully")
                
                if advanced_success == len(advanced_packages):
                    print("\n🚀 Full installation complete! All features are available.")
                else:
                    print("\n⚠️ Some advanced features may not work properly due to installation issues.")
            else:
                print("\n📝 Skipping advanced features. You can install them later by running this script again.")
                
        except KeyboardInterrupt:
            print("\n\n⏹️ Installation interrupted by user.")
    else:
        print("\n❌ Core installation failed. Please check your Python environment and try again.")
    
    print("\n" + "=" * 50)
    print("To run the application:")
    print("1. streamlit run enhanced_ecg_app.py")
    print("2. Open your browser to the displayed URL")
    print("\nNote: Make sure you have the model files (model_test.pkl) in the same directory.")

if __name__ == "__main__":
    main()
