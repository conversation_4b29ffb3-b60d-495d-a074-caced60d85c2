# -*- coding: utf-8 -*-
"""
Contains all possible non-ASCII unicode numbers.
"""

# Rather than determine what unicode characters are numeric on the fly which
# would incur a startup runtime penalty, the hex values are hard-coded below.
numeric_hex = (
    0xB2,
    0xB3,
    0xB9,
    0xBC,
    0xBD,
    0xBE,
    0x660,
    0x661,
    0x662,
    0x663,
    0x664,
    0x665,
    0x666,
    0x667,
    0x668,
    0x669,
    0x6F0,
    0x6F1,
    0x6F2,
    0x6F3,
    0x6F4,
    0x6F5,
    0x6F6,
    0x6F7,
    0x6F8,
    0x6F9,
    0x7C0,
    0x7C1,
    0x7C2,
    0x7C3,
    0x7C4,
    0x7C5,
    0x7C6,
    0x7C7,
    0x7C8,
    0x7C9,
    0x966,
    0x967,
    0x968,
    0x969,
    0x96A,
    0x96B,
    0x96C,
    0x96D,
    0x96E,
    0x96F,
    0x9E6,
    0x9E7,
    0x9E8,
    0x9E9,
    0x9EA,
    0x9EB,
    0x9EC,
    0x9ED,
    0x9EE,
    0x9EF,
    0x9F4,
    0x9F5,
    0x9F6,
    0x9F7,
    0x9F8,
    0x9F9,
    0xA66,
    0xA67,
    0xA68,
    0xA69,
    0xA6A,
    0xA6B,
    0xA6C,
    0xA6D,
    0xA6E,
    0xA6F,
    0xAE6,
    0xAE7,
    0xAE8,
    0xAE9,
    0xAEA,
    0xAEB,
    0xAEC,
    0xAED,
    0xAEE,
    0xAEF,
    0xB66,
    0xB67,
    0xB68,
    0xB69,
    0xB6A,
    0xB6B,
    0xB6C,
    0xB6D,
    0xB6E,
    0xB6F,
    0xB72,
    0xB73,
    0xB74,
    0xB75,
    0xB76,
    0xB77,
    0xBE6,
    0xBE7,
    0xBE8,
    0xBE9,
    0xBEA,
    0xBEB,
    0xBEC,
    0xBED,
    0xBEE,
    0xBEF,
    0xBF0,
    0xBF1,
    0xBF2,
    0xC66,
    0xC67,
    0xC68,
    0xC69,
    0xC6A,
    0xC6B,
    0xC6C,
    0xC6D,
    0xC6E,
    0xC6F,
    0xC78,
    0xC79,
    0xC7A,
    0xC7B,
    0xC7C,
    0xC7D,
    0xC7E,
    0xCE6,
    0xCE7,
    0xCE8,
    0xCE9,
    0xCEA,
    0xCEB,
    0xCEC,
    0xCED,
    0xCEE,
    0xCEF,
    0xD58,
    0xD59,
    0xD5A,
    0xD5B,
    0xD5C,
    0xD5D,
    0xD5E,
    0xD66,
    0xD67,
    0xD68,
    0xD69,
    0xD6A,
    0xD6B,
    0xD6C,
    0xD6D,
    0xD6E,
    0xD6F,
    0xD70,
    0xD71,
    0xD72,
    0xD73,
    0xD74,
    0xD75,
    0xD76,
    0xD77,
    0xD78,
    0xDE6,
    0xDE7,
    0xDE8,
    0xDE9,
    0xDEA,
    0xDEB,
    0xDEC,
    0xDED,
    0xDEE,
    0xDEF,
    0xE50,
    0xE51,
    0xE52,
    0xE53,
    0xE54,
    0xE55,
    0xE56,
    0xE57,
    0xE58,
    0xE59,
    0xED0,
    0xED1,
    0xED2,
    0xED3,
    0xED4,
    0xED5,
    0xED6,
    0xED7,
    0xED8,
    0xED9,
    0xF20,
    0xF21,
    0xF22,
    0xF23,
    0xF24,
    0xF25,
    0xF26,
    0xF27,
    0xF28,
    0xF29,
    0xF2A,
    0xF2B,
    0xF2C,
    0xF2D,
    0xF2E,
    0xF2F,
    0xF30,
    0xF31,
    0xF32,
    0xF33,
    0x1040,
    0x1041,
    0x1042,
    0x1043,
    0x1044,
    0x1045,
    0x1046,
    0x1047,
    0x1048,
    0x1049,
    0x1090,
    0x1091,
    0x1092,
    0x1093,
    0x1094,
    0x1095,
    0x1096,
    0x1097,
    0x1098,
    0x1099,
    0x1369,
    0x136A,
    0x136B,
    0x136C,
    0x136D,
    0x136E,
    0x136F,
    0x1370,
    0x1371,
    0x1372,
    0x1373,
    0x1374,
    0x1375,
    0x1376,
    0x1377,
    0x1378,
    0x1379,
    0x137A,
    0x137B,
    0x137C,
    0x16EE,
    0x16EF,
    0x16F0,
    0x17E0,
    0x17E1,
    0x17E2,
    0x17E3,
    0x17E4,
    0x17E5,
    0x17E6,
    0x17E7,
    0x17E8,
    0x17E9,
    0x17F0,
    0x17F1,
    0x17F2,
    0x17F3,
    0x17F4,
    0x17F5,
    0x17F6,
    0x17F7,
    0x17F8,
    0x17F9,
    0x1810,
    0x1811,
    0x1812,
    0x1813,
    0x1814,
    0x1815,
    0x1816,
    0x1817,
    0x1818,
    0x1819,
    0x1946,
    0x1947,
    0x1948,
    0x1949,
    0x194A,
    0x194B,
    0x194C,
    0x194D,
    0x194E,
    0x194F,
    0x19D0,
    0x19D1,
    0x19D2,
    0x19D3,
    0x19D4,
    0x19D5,
    0x19D6,
    0x19D7,
    0x19D8,
    0x19D9,
    0x19DA,
    0x1A80,
    0x1A81,
    0x1A82,
    0x1A83,
    0x1A84,
    0x1A85,
    0x1A86,
    0x1A87,
    0x1A88,
    0x1A89,
    0x1A90,
    0x1A91,
    0x1A92,
    0x1A93,
    0x1A94,
    0x1A95,
    0x1A96,
    0x1A97,
    0x1A98,
    0x1A99,
    0x1B50,
    0x1B51,
    0x1B52,
    0x1B53,
    0x1B54,
    0x1B55,
    0x1B56,
    0x1B57,
    0x1B58,
    0x1B59,
    0x1BB0,
    0x1BB1,
    0x1BB2,
    0x1BB3,
    0x1BB4,
    0x1BB5,
    0x1BB6,
    0x1BB7,
    0x1BB8,
    0x1BB9,
    0x1C40,
    0x1C41,
    0x1C42,
    0x1C43,
    0x1C44,
    0x1C45,
    0x1C46,
    0x1C47,
    0x1C48,
    0x1C49,
    0x1C50,
    0x1C51,
    0x1C52,
    0x1C53,
    0x1C54,
    0x1C55,
    0x1C56,
    0x1C57,
    0x1C58,
    0x1C59,
    0x2070,
    0x2074,
    0x2075,
    0x2076,
    0x2077,
    0x2078,
    0x2079,
    0x2080,
    0x2081,
    0x2082,
    0x2083,
    0x2084,
    0x2085,
    0x2086,
    0x2087,
    0x2088,
    0x2089,
    0x2150,
    0x2151,
    0x2152,
    0x2153,
    0x2154,
    0x2155,
    0x2156,
    0x2157,
    0x2158,
    0x2159,
    0x215A,
    0x215B,
    0x215C,
    0x215D,
    0x215E,
    0x215F,
    0x2160,
    0x2161,
    0x2162,
    0x2163,
    0x2164,
    0x2165,
    0x2166,
    0x2167,
    0x2168,
    0x2169,
    0x216A,
    0x216B,
    0x216C,
    0x216D,
    0x216E,
    0x216F,
    0x2170,
    0x2171,
    0x2172,
    0x2173,
    0x2174,
    0x2175,
    0x2176,
    0x2177,
    0x2178,
    0x2179,
    0x217A,
    0x217B,
    0x217C,
    0x217D,
    0x217E,
    0x217F,
    0x2180,
    0x2181,
    0x2182,
    0x2185,
    0x2186,
    0x2187,
    0x2188,
    0x2189,
    0x2460,
    0x2461,
    0x2462,
    0x2463,
    0x2464,
    0x2465,
    0x2466,
    0x2467,
    0x2468,
    0x2469,
    0x246A,
    0x246B,
    0x246C,
    0x246D,
    0x246E,
    0x246F,
    0x2470,
    0x2471,
    0x2472,
    0x2473,
    0x2474,
    0x2475,
    0x2476,
    0x2477,
    0x2478,
    0x2479,
    0x247A,
    0x247B,
    0x247C,
    0x247D,
    0x247E,
    0x247F,
    0x2480,
    0x2481,
    0x2482,
    0x2483,
    0x2484,
    0x2485,
    0x2486,
    0x2487,
    0x2488,
    0x2489,
    0x248A,
    0x248B,
    0x248C,
    0x248D,
    0x248E,
    0x248F,
    0x2490,
    0x2491,
    0x2492,
    0x2493,
    0x2494,
    0x2495,
    0x2496,
    0x2497,
    0x2498,
    0x2499,
    0x249A,
    0x249B,
    0x24EA,
    0x24EB,
    0x24EC,
    0x24ED,
    0x24EE,
    0x24EF,
    0x24F0,
    0x24F1,
    0x24F2,
    0x24F3,
    0x24F4,
    0x24F5,
    0x24F6,
    0x24F7,
    0x24F8,
    0x24F9,
    0x24FA,
    0x24FB,
    0x24FC,
    0x24FD,
    0x24FE,
    0x24FF,
    0x2776,
    0x2777,
    0x2778,
    0x2779,
    0x277A,
    0x277B,
    0x277C,
    0x277D,
    0x277E,
    0x277F,
    0x2780,
    0x2781,
    0x2782,
    0x2783,
    0x2784,
    0x2785,
    0x2786,
    0x2787,
    0x2788,
    0x2789,
    0x278A,
    0x278B,
    0x278C,
    0x278D,
    0x278E,
    0x278F,
    0x2790,
    0x2791,
    0x2792,
    0x2793,
    0x2CFD,
    0x3007,
    0x3021,
    0x3022,
    0x3023,
    0x3024,
    0x3025,
    0x3026,
    0x3027,
    0x3028,
    0x3029,
    0x3038,
    0x3039,
    0x303A,
    0x3192,
    0x3193,
    0x3194,
    0x3195,
    0x3220,
    0x3221,
    0x3222,
    0x3223,
    0x3224,
    0x3225,
    0x3226,
    0x3227,
    0x3228,
    0x3229,
    0x3248,
    0x3249,
    0x324A,
    0x324B,
    0x324C,
    0x324D,
    0x324E,
    0x324F,
    0x3251,
    0x3252,
    0x3253,
    0x3254,
    0x3255,
    0x3256,
    0x3257,
    0x3258,
    0x3259,
    0x325A,
    0x325B,
    0x325C,
    0x325D,
    0x325E,
    0x325F,
    0x3280,
    0x3281,
    0x3282,
    0x3283,
    0x3284,
    0x3285,
    0x3286,
    0x3287,
    0x3288,
    0x3289,
    0x32B1,
    0x32B2,
    0x32B3,
    0x32B4,
    0x32B5,
    0x32B6,
    0x32B7,
    0x32B8,
    0x32B9,
    0x32BA,
    0x32BB,
    0x32BC,
    0x32BD,
    0x32BE,
    0x32BF,
    0x3405,
    0x3483,
    0x382A,
    0x3B4D,
    0x4E00,
    0x4E03,
    0x4E07,
    0x4E09,
    0x4E5D,
    0x4E8C,
    0x4E94,
    0x4E96,
    0x4EBF,
    0x4EC0,
    0x4EDF,
    0x4EE8,
    0x4F0D,
    0x4F70,
    0x5104,
    0x5146,
    0x5169,
    0x516B,
    0x516D,
    0x5341,
    0x5343,
    0x5344,
    0x5345,
    0x534C,
    0x53C1,
    0x53C2,
    0x53C3,
    0x53C4,
    0x56DB,
    0x58F1,
    0x58F9,
    0x5E7A,
    0x5EFE,
    0x5EFF,
    0x5F0C,
    0x5F0D,
    0x5F0E,
    0x5F10,
    0x62FE,
    0x634C,
    0x67D2,
    0x6F06,
    0x7396,
    0x767E,
    0x8086,
    0x842C,
    0x8CAE,
    0x8CB3,
    0x8D30,
    0x9621,
    0x9646,
    0x964C,
    0x9678,
    0x96F6,
    0xA620,
    0xA621,
    0xA622,
    0xA623,
    0xA624,
    0xA625,
    0xA626,
    0xA627,
    0xA628,
    0xA629,
    0xA6E6,
    0xA6E7,
    0xA6E8,
    0xA6E9,
    0xA6EA,
    0xA6EB,
    0xA6EC,
    0xA6ED,
    0xA6EE,
    0xA6EF,
    0xA830,
    0xA831,
    0xA832,
    0xA833,
    0xA834,
    0xA835,
    0xA8D0,
    0xA8D1,
    0xA8D2,
    0xA8D3,
    0xA8D4,
    0xA8D5,
    0xA8D6,
    0xA8D7,
    0xA8D8,
    0xA8D9,
    0xA900,
    0xA901,
    0xA902,
    0xA903,
    0xA904,
    0xA905,
    0xA906,
    0xA907,
    0xA908,
    0xA909,
    0xA9D0,
    0xA9D1,
    0xA9D2,
    0xA9D3,
    0xA9D4,
    0xA9D5,
    0xA9D6,
    0xA9D7,
    0xA9D8,
    0xA9D9,
    0xA9F0,
    0xA9F1,
    0xA9F2,
    0xA9F3,
    0xA9F4,
    0xA9F5,
    0xA9F6,
    0xA9F7,
    0xA9F8,
    0xA9F9,
    0xAA50,
    0xAA51,
    0xAA52,
    0xAA53,
    0xAA54,
    0xAA55,
    0xAA56,
    0xAA57,
    0xAA58,
    0xAA59,
    0xABF0,
    0xABF1,
    0xABF2,
    0xABF3,
    0xABF4,
    0xABF5,
    0xABF6,
    0xABF7,
    0xABF8,
    0xABF9,
    0xF96B,
    0xF973,
    0xF978,
    0xF9B2,
    0xF9D1,
    0xF9D3,
    0xF9FD,
    0xFF10,
    0xFF11,
    0xFF12,
    0xFF13,
    0xFF14,
    0xFF15,
    0xFF16,
    0xFF17,
    0xFF18,
    0xFF19,
    0x10107,
    0x10108,
    0x10109,
    0x1010A,
    0x1010B,
    0x1010C,
    0x1010D,
    0x1010E,
    0x1010F,
    0x10110,
    0x10111,
    0x10112,
    0x10113,
    0x10114,
    0x10115,
    0x10116,
    0x10117,
    0x10118,
    0x10119,
    0x1011A,
    0x1011B,
    0x1011C,
    0x1011D,
    0x1011E,
    0x1011F,
    0x10120,
    0x10121,
    0x10122,
    0x10123,
    0x10124,
    0x10125,
    0x10126,
    0x10127,
    0x10128,
    0x10129,
    0x1012A,
    0x1012B,
    0x1012C,
    0x1012D,
    0x1012E,
    0x1012F,
    0x10130,
    0x10131,
    0x10132,
    0x10133,
    0x10140,
    0x10141,
    0x10142,
    0x10143,
    0x10144,
    0x10145,
    0x10146,
    0x10147,
    0x10148,
    0x10149,
    0x1014A,
    0x1014B,
    0x1014C,
    0x1014D,
    0x1014E,
    0x1014F,
    0x10150,
    0x10151,
    0x10152,
    0x10153,
    0x10154,
    0x10155,
    0x10156,
    0x10157,
    0x10158,
    0x10159,
    0x1015A,
    0x1015B,
    0x1015C,
    0x1015D,
    0x1015E,
    0x1015F,
    0x10160,
    0x10161,
    0x10162,
    0x10163,
    0x10164,
    0x10165,
    0x10166,
    0x10167,
    0x10168,
    0x10169,
    0x1016A,
    0x1016B,
    0x1016C,
    0x1016D,
    0x1016E,
    0x1016F,
    0x10170,
    0x10171,
    0x10172,
    0x10173,
    0x10174,
    0x10175,
    0x10176,
    0x10177,
    0x10178,
    0x1018A,
    0x1018B,
    0x102E1,
    0x102E2,
    0x102E3,
    0x102E4,
    0x102E5,
    0x102E6,
    0x102E7,
    0x102E8,
    0x102E9,
    0x102EA,
    0x102EB,
    0x102EC,
    0x102ED,
    0x102EE,
    0x102EF,
    0x102F0,
    0x102F1,
    0x102F2,
    0x102F3,
    0x102F4,
    0x102F5,
    0x102F6,
    0x102F7,
    0x102F8,
    0x102F9,
    0x102FA,
    0x102FB,
    0x10320,
    0x10321,
    0x10322,
    0x10323,
    0x10341,
    0x1034A,
    0x103D1,
    0x103D2,
    0x103D3,
    0x103D4,
    0x103D5,
    0x104A0,
    0x104A1,
    0x104A2,
    0x104A3,
    0x104A4,
    0x104A5,
    0x104A6,
    0x104A7,
    0x104A8,
    0x104A9,
    0x10858,
    0x10859,
    0x1085A,
    0x1085B,
    0x1085C,
    0x1085D,
    0x1085E,
    0x1085F,
    0x10879,
    0x1087A,
    0x1087B,
    0x1087C,
    0x1087D,
    0x1087E,
    0x1087F,
    0x108A7,
    0x108A8,
    0x108A9,
    0x108AA,
    0x108AB,
    0x108AC,
    0x108AD,
    0x108AE,
    0x108AF,
    0x108FB,
    0x108FC,
    0x108FD,
    0x108FE,
    0x108FF,
    0x10916,
    0x10917,
    0x10918,
    0x10919,
    0x1091A,
    0x1091B,
    0x109BC,
    0x109BD,
    0x109C0,
    0x109C1,
    0x109C2,
    0x109C3,
    0x109C4,
    0x109C5,
    0x109C6,
    0x109C7,
    0x109C8,
    0x109C9,
    0x109CA,
    0x109CB,
    0x109CC,
    0x109CD,
    0x109CE,
    0x109CF,
    0x109D2,
    0x109D3,
    0x109D4,
    0x109D5,
    0x109D6,
    0x109D7,
    0x109D8,
    0x109D9,
    0x109DA,
    0x109DB,
    0x109DC,
    0x109DD,
    0x109DE,
    0x109DF,
    0x109E0,
    0x109E1,
    0x109E2,
    0x109E3,
    0x109E4,
    0x109E5,
    0x109E6,
    0x109E7,
    0x109E8,
    0x109E9,
    0x109EA,
    0x109EB,
    0x109EC,
    0x109ED,
    0x109EE,
    0x109EF,
    0x109F0,
    0x109F1,
    0x109F2,
    0x109F3,
    0x109F4,
    0x109F5,
    0x109F6,
    0x109F7,
    0x109F8,
    0x109F9,
    0x109FA,
    0x109FB,
    0x109FC,
    0x109FD,
    0x109FE,
    0x109FF,
    0x10A40,
    0x10A41,
    0x10A42,
    0x10A43,
    0x10A44,
    0x10A45,
    0x10A46,
    0x10A47,
    0x10A48,
    0x10A7D,
    0x10A7E,
    0x10A9D,
    0x10A9E,
    0x10A9F,
    0x10AEB,
    0x10AEC,
    0x10AED,
    0x10AEE,
    0x10AEF,
    0x10B58,
    0x10B59,
    0x10B5A,
    0x10B5B,
    0x10B5C,
    0x10B5D,
    0x10B5E,
    0x10B5F,
    0x10B78,
    0x10B79,
    0x10B7A,
    0x10B7B,
    0x10B7C,
    0x10B7D,
    0x10B7E,
    0x10B7F,
    0x10BA9,
    0x10BAA,
    0x10BAB,
    0x10BAC,
    0x10BAD,
    0x10BAE,
    0x10BAF,
    0x10CFA,
    0x10CFB,
    0x10CFC,
    0x10CFD,
    0x10CFE,
    0x10CFF,
    0x10D30,
    0x10D31,
    0x10D32,
    0x10D33,
    0x10D34,
    0x10D35,
    0x10D36,
    0x10D37,
    0x10D38,
    0x10D39,
    0x10E60,
    0x10E61,
    0x10E62,
    0x10E63,
    0x10E64,
    0x10E65,
    0x10E66,
    0x10E67,
    0x10E68,
    0x10E69,
    0x10E6A,
    0x10E6B,
    0x10E6C,
    0x10E6D,
    0x10E6E,
    0x10E6F,
    0x10E70,
    0x10E71,
    0x10E72,
    0x10E73,
    0x10E74,
    0x10E75,
    0x10E76,
    0x10E77,
    0x10E78,
    0x10E79,
    0x10E7A,
    0x10E7B,
    0x10E7C,
    0x10E7D,
    0x10E7E,
    0x10F1D,
    0x10F1E,
    0x10F1F,
    0x10F20,
    0x10F21,
    0x10F22,
    0x10F23,
    0x10F24,
    0x10F25,
    0x10F26,
    0x10F51,
    0x10F52,
    0x10F53,
    0x10F54,
    0x10FC5,
    0x10FC6,
    0x10FC7,
    0x10FC8,
    0x10FC9,
    0x10FCA,
    0x10FCB,
    0x11052,
    0x11053,
    0x11054,
    0x11055,
    0x11056,
    0x11057,
    0x11058,
    0x11059,
    0x1105A,
    0x1105B,
    0x1105C,
    0x1105D,
    0x1105E,
    0x1105F,
    0x11060,
    0x11061,
    0x11062,
    0x11063,
    0x11064,
    0x11065,
    0x11066,
    0x11067,
    0x11068,
    0x11069,
    0x1106A,
    0x1106B,
    0x1106C,
    0x1106D,
    0x1106E,
    0x1106F,
    0x110F0,
    0x110F1,
    0x110F2,
    0x110F3,
    0x110F4,
    0x110F5,
    0x110F6,
    0x110F7,
    0x110F8,
    0x110F9,
    0x11136,
    0x11137,
    0x11138,
    0x11139,
    0x1113A,
    0x1113B,
    0x1113C,
    0x1113D,
    0x1113E,
    0x1113F,
    0x111D0,
    0x111D1,
    0x111D2,
    0x111D3,
    0x111D4,
    0x111D5,
    0x111D6,
    0x111D7,
    0x111D8,
    0x111D9,
    0x111E1,
    0x111E2,
    0x111E3,
    0x111E4,
    0x111E5,
    0x111E6,
    0x111E7,
    0x111E8,
    0x111E9,
    0x111EA,
    0x111EB,
    0x111EC,
    0x111ED,
    0x111EE,
    0x111EF,
    0x111F0,
    0x111F1,
    0x111F2,
    0x111F3,
    0x111F4,
    0x112F0,
    0x112F1,
    0x112F2,
    0x112F3,
    0x112F4,
    0x112F5,
    0x112F6,
    0x112F7,
    0x112F8,
    0x112F9,
    0x11450,
    0x11451,
    0x11452,
    0x11453,
    0x11454,
    0x11455,
    0x11456,
    0x11457,
    0x11458,
    0x11459,
    0x114D0,
    0x114D1,
    0x114D2,
    0x114D3,
    0x114D4,
    0x114D5,
    0x114D6,
    0x114D7,
    0x114D8,
    0x114D9,
    0x11650,
    0x11651,
    0x11652,
    0x11653,
    0x11654,
    0x11655,
    0x11656,
    0x11657,
    0x11658,
    0x11659,
    0x116C0,
    0x116C1,
    0x116C2,
    0x116C3,
    0x116C4,
    0x116C5,
    0x116C6,
    0x116C7,
    0x116C8,
    0x116C9,
    0x11730,
    0x11731,
    0x11732,
    0x11733,
    0x11734,
    0x11735,
    0x11736,
    0x11737,
    0x11738,
    0x11739,
    0x1173A,
    0x1173B,
    0x118E0,
    0x118E1,
    0x118E2,
    0x118E3,
    0x118E4,
    0x118E5,
    0x118E6,
    0x118E7,
    0x118E8,
    0x118E9,
    0x118EA,
    0x118EB,
    0x118EC,
    0x118ED,
    0x118EE,
    0x118EF,
    0x118F0,
    0x118F1,
    0x118F2,
    0x11950,
    0x11951,
    0x11952,
    0x11953,
    0x11954,
    0x11955,
    0x11956,
    0x11957,
    0x11958,
    0x11959,
    0x11C50,
    0x11C51,
    0x11C52,
    0x11C53,
    0x11C54,
    0x11C55,
    0x11C56,
    0x11C57,
    0x11C58,
    0x11C59,
    0x11C5A,
    0x11C5B,
    0x11C5C,
    0x11C5D,
    0x11C5E,
    0x11C5F,
    0x11C60,
    0x11C61,
    0x11C62,
    0x11C63,
    0x11C64,
    0x11C65,
    0x11C66,
    0x11C67,
    0x11C68,
    0x11C69,
    0x11C6A,
    0x11C6B,
    0x11C6C,
    0x11D50,
    0x11D51,
    0x11D52,
    0x11D53,
    0x11D54,
    0x11D55,
    0x11D56,
    0x11D57,
    0x11D58,
    0x11D59,
    0x11DA0,
    0x11DA1,
    0x11DA2,
    0x11DA3,
    0x11DA4,
    0x11DA5,
    0x11DA6,
    0x11DA7,
    0x11DA8,
    0x11DA9,
    0x11FC0,
    0x11FC1,
    0x11FC2,
    0x11FC3,
    0x11FC4,
    0x11FC5,
    0x11FC6,
    0x11FC7,
    0x11FC8,
    0x11FC9,
    0x11FCA,
    0x11FCB,
    0x11FCC,
    0x11FCD,
    0x11FCE,
    0x11FCF,
    0x11FD0,
    0x11FD1,
    0x11FD2,
    0x11FD3,
    0x11FD4,
    0x12400,
    0x12401,
    0x12402,
    0x12403,
    0x12404,
    0x12405,
    0x12406,
    0x12407,
    0x12408,
    0x12409,
    0x1240A,
    0x1240B,
    0x1240C,
    0x1240D,
    0x1240E,
    0x1240F,
    0x12410,
    0x12411,
    0x12412,
    0x12413,
    0x12414,
    0x12415,
    0x12416,
    0x12417,
    0x12418,
    0x12419,
    0x1241A,
    0x1241B,
    0x1241C,
    0x1241D,
    0x1241E,
    0x1241F,
    0x12420,
    0x12421,
    0x12422,
    0x12423,
    0x12424,
    0x12425,
    0x12426,
    0x12427,
    0x12428,
    0x12429,
    0x1242A,
    0x1242B,
    0x1242C,
    0x1242D,
    0x1242E,
    0x1242F,
    0x12430,
    0x12431,
    0x12432,
    0x12433,
    0x12434,
    0x12435,
    0x12436,
    0x12437,
    0x12438,
    0x12439,
    0x1243A,
    0x1243B,
    0x1243C,
    0x1243D,
    0x1243E,
    0x1243F,
    0x12440,
    0x12441,
    0x12442,
    0x12443,
    0x12444,
    0x12445,
    0x12446,
    0x12447,
    0x12448,
    0x12449,
    0x1244A,
    0x1244B,
    0x1244C,
    0x1244D,
    0x1244E,
    0x1244F,
    0x12450,
    0x12451,
    0x12452,
    0x12453,
    0x12454,
    0x12455,
    0x12456,
    0x12457,
    0x12458,
    0x12459,
    0x1245A,
    0x1245B,
    0x1245C,
    0x1245D,
    0x1245E,
    0x1245F,
    0x12460,
    0x12461,
    0x12462,
    0x12463,
    0x12464,
    0x12465,
    0x12466,
    0x12467,
    0x12468,
    0x12469,
    0x1246A,
    0x1246B,
    0x1246C,
    0x1246D,
    0x1246E,
    0x16A60,
    0x16A61,
    0x16A62,
    0x16A63,
    0x16A64,
    0x16A65,
    0x16A66,
    0x16A67,
    0x16A68,
    0x16A69,
    0x16AC0,
    0x16AC1,
    0x16AC2,
    0x16AC3,
    0x16AC4,
    0x16AC5,
    0x16AC6,
    0x16AC7,
    0x16AC8,
    0x16AC9,
    0x16B50,
    0x16B51,
    0x16B52,
    0x16B53,
    0x16B54,
    0x16B55,
    0x16B56,
    0x16B57,
    0x16B58,
    0x16B59,
    0x16B5B,
    0x16B5C,
    0x16B5D,
    0x16B5E,
    0x16B5F,
    0x16B60,
    0x16B61,
    0x16E80,
    0x16E81,
    0x16E82,
    0x16E83,
    0x16E84,
    0x16E85,
    0x16E86,
    0x16E87,
    0x16E88,
    0x16E89,
    0x16E8A,
    0x16E8B,
    0x16E8C,
    0x16E8D,
    0x16E8E,
    0x16E8F,
    0x16E90,
    0x16E91,
    0x16E92,
    0x16E93,
    0x16E94,
    0x16E95,
    0x16E96,
    0x1D2E0,
    0x1D2E1,
    0x1D2E2,
    0x1D2E3,
    0x1D2E4,
    0x1D2E5,
    0x1D2E6,
    0x1D2E7,
    0x1D2E8,
    0x1D2E9,
    0x1D2EA,
    0x1D2EB,
    0x1D2EC,
    0x1D2ED,
    0x1D2EE,
    0x1D2EF,
    0x1D2F0,
    0x1D2F1,
    0x1D2F2,
    0x1D2F3,
    0x1D360,
    0x1D361,
    0x1D362,
    0x1D363,
    0x1D364,
    0x1D365,
    0x1D366,
    0x1D367,
    0x1D368,
    0x1D369,
    0x1D36A,
    0x1D36B,
    0x1D36C,
    0x1D36D,
    0x1D36E,
    0x1D36F,
    0x1D370,
    0x1D371,
    0x1D372,
    0x1D373,
    0x1D374,
    0x1D375,
    0x1D376,
    0x1D377,
    0x1D378,
    0x1D7CE,
    0x1D7CF,
    0x1D7D0,
    0x1D7D1,
    0x1D7D2,
    0x1D7D3,
    0x1D7D4,
    0x1D7D5,
    0x1D7D6,
    0x1D7D7,
    0x1D7D8,
    0x1D7D9,
    0x1D7DA,
    0x1D7DB,
    0x1D7DC,
    0x1D7DD,
    0x1D7DE,
    0x1D7DF,
    0x1D7E0,
    0x1D7E1,
    0x1D7E2,
    0x1D7E3,
    0x1D7E4,
    0x1D7E5,
    0x1D7E6,
    0x1D7E7,
    0x1D7E8,
    0x1D7E9,
    0x1D7EA,
    0x1D7EB,
    0x1D7EC,
    0x1D7ED,
    0x1D7EE,
    0x1D7EF,
    0x1D7F0,
    0x1D7F1,
    0x1D7F2,
    0x1D7F3,
    0x1D7F4,
    0x1D7F5,
    0x1D7F6,
    0x1D7F7,
    0x1D7F8,
    0x1D7F9,
    0x1D7FA,
    0x1D7FB,
    0x1D7FC,
    0x1D7FD,
    0x1D7FE,
    0x1D7FF,
    0x1E140,
    0x1E141,
    0x1E142,
    0x1E143,
    0x1E144,
    0x1E145,
    0x1E146,
    0x1E147,
    0x1E148,
    0x1E149,
    0x1E2F0,
    0x1E2F1,
    0x1E2F2,
    0x1E2F3,
    0x1E2F4,
    0x1E2F5,
    0x1E2F6,
    0x1E2F7,
    0x1E2F8,
    0x1E2F9,
    0x1E8C7,
    0x1E8C8,
    0x1E8C9,
    0x1E8CA,
    0x1E8CB,
    0x1E8CC,
    0x1E8CD,
    0x1E8CE,
    0x1E8CF,
    0x1E950,
    0x1E951,
    0x1E952,
    0x1E953,
    0x1E954,
    0x1E955,
    0x1E956,
    0x1E957,
    0x1E958,
    0x1E959,
    0x1EC71,
    0x1EC72,
    0x1EC73,
    0x1EC74,
    0x1EC75,
    0x1EC76,
    0x1EC77,
    0x1EC78,
    0x1EC79,
    0x1EC7A,
    0x1EC7B,
    0x1EC7C,
    0x1EC7D,
    0x1EC7E,
    0x1EC7F,
    0x1EC80,
    0x1EC81,
    0x1EC82,
    0x1EC83,
    0x1EC84,
    0x1EC85,
    0x1EC86,
    0x1EC87,
    0x1EC88,
    0x1EC89,
    0x1EC8A,
    0x1EC8B,
    0x1EC8C,
    0x1EC8D,
    0x1EC8E,
    0x1EC8F,
    0x1EC90,
    0x1EC91,
    0x1EC92,
    0x1EC93,
    0x1EC94,
    0x1EC95,
    0x1EC96,
    0x1EC97,
    0x1EC98,
    0x1EC99,
    0x1EC9A,
    0x1EC9B,
    0x1EC9C,
    0x1EC9D,
    0x1EC9E,
    0x1EC9F,
    0x1ECA0,
    0x1ECA1,
    0x1ECA2,
    0x1ECA3,
    0x1ECA4,
    0x1ECA5,
    0x1ECA6,
    0x1ECA7,
    0x1ECA8,
    0x1ECA9,
    0x1ECAA,
    0x1ECAB,
    0x1ECAD,
    0x1ECAE,
    0x1ECAF,
    0x1ECB1,
    0x1ECB2,
    0x1ECB3,
    0x1ECB4,
    0x1ED01,
    0x1ED02,
    0x1ED03,
    0x1ED04,
    0x1ED05,
    0x1ED06,
    0x1ED07,
    0x1ED08,
    0x1ED09,
    0x1ED0A,
    0x1ED0B,
    0x1ED0C,
    0x1ED0D,
    0x1ED0E,
    0x1ED0F,
    0x1ED10,
    0x1ED11,
    0x1ED12,
    0x1ED13,
    0x1ED14,
    0x1ED15,
    0x1ED16,
    0x1ED17,
    0x1ED18,
    0x1ED19,
    0x1ED1A,
    0x1ED1B,
    0x1ED1C,
    0x1ED1D,
    0x1ED1E,
    0x1ED1F,
    0x1ED20,
    0x1ED21,
    0x1ED22,
    0x1ED23,
    0x1ED24,
    0x1ED25,
    0x1ED26,
    0x1ED27,
    0x1ED28,
    0x1ED29,
    0x1ED2A,
    0x1ED2B,
    0x1ED2C,
    0x1ED2D,
    0x1ED2F,
    0x1ED30,
    0x1ED31,
    0x1ED32,
    0x1ED33,
    0x1ED34,
    0x1ED35,
    0x1ED36,
    0x1ED37,
    0x1ED38,
    0x1ED39,
    0x1ED3A,
    0x1ED3B,
    0x1ED3C,
    0x1ED3D,
    0x1F100,
    0x1F101,
    0x1F102,
    0x1F103,
    0x1F104,
    0x1F105,
    0x1F106,
    0x1F107,
    0x1F108,
    0x1F109,
    0x1F10A,
    0x1F10B,
    0x1F10C,
    0x1FBF0,
    0x1FBF1,
    0x1FBF2,
    0x1FBF3,
    0x1FBF4,
    0x1FBF5,
    0x1FBF6,
    0x1FBF7,
    0x1FBF8,
    0x1FBF9,
    0x20001,
    0x20064,
    0x200E2,
    0x20121,
    0x2092A,
    0x20983,
    0x2098C,
    0x2099C,
    0x20AEA,
    0x20AFD,
    0x20B19,
    0x22390,
    0x22998,
    0x23B1B,
    0x2626D,
    0x2F890,
)
